/* ========================================
   Registration Page Specific Styles
   ======================================== */

/* Registration Page Layout */
.registration-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
    padding-top: 0; /* Match home page header styling */
    position: relative;
    overflow-x: hidden;
    /* Performance Optimization */
    will-change: scroll-position;
    transform: translateZ(0); /* Force hardware acceleration */
    contain: layout style paint; /* CSS Containment for better performance */
}

/* Enhanced Background Design - Optimized for Performance */
.registration-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 25% 20%, rgba(46, 89, 144, 0.1) 0%, transparent 40%),
        radial-gradient(circle at 75% 80%, rgba(255, 140, 66, 0.08) 0%, transparent 40%),
        radial-gradient(circle at 90% 40%, rgba(76, 175, 80, 0.06) 0%, transparent 30%),
        linear-gradient(135deg, transparent 0%, rgba(46, 89, 144, 0.02) 50%, transparent 100%);
    pointer-events: none;
    z-index: 1;
    /* Performance Optimization */
    will-change: transform;
    transform: translateZ(0);
}

.registration-page::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        radial-gradient(2px 2px at 40% 30%, rgba(46, 89, 144, 0.2), transparent),
        radial-gradient(2px 2px at 70% 70%, rgba(255, 140, 66, 0.15), transparent),
        radial-gradient(1px 1px at 20% 80%, rgba(76, 175, 80, 0.15), transparent),
        radial-gradient(1px 1px at 90% 10%, rgba(46, 89, 144, 0.1), transparent);
    background-size: 80px 80px, 120px 120px, 60px 60px, 100px 100px;
    background-position: 0 0, 40px 40px, 20px 20px, 60px 60px;
    /* Reduced animation intensity for better performance */
    animation: backgroundFloat 30s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
    opacity: 0.3;
    /* Performance Optimization */
    will-change: transform;
    transform: translateZ(0);
}

@keyframes backgroundFloat {
    0%, 100% {
        transform: translate3d(0, 0, 0) rotate(0deg);
    }
    25% {
        transform: translate3d(-5px, -8px, 0) rotate(0.5deg);
    }
    50% {
        transform: translate3d(8px, -5px, 0) rotate(-0.5deg);
    }
    75% {
        transform: translate3d(-3px, 5px, 0) rotate(0.2deg);
    }
}

/* Content Layer */
.registration-page > * {
    position: relative;
    z-index: 2;
}

/* Registration Header Section */
.registration-header {
    /* Registration-specific styling */
}

.section-header-wrapper {
    position: relative;
    z-index: 2;
    text-align: center;
}

.registration-title-container {
    /* Registration-specific title container styling */
}

/* Registration-specific subtitle styling */
.registration-early-bird {
    font-family: var(--font-family-primary);
    font-size: 1.6rem;
    font-weight: var(--font-weight-bold);
    color: white;
    text-transform: uppercase;
    letter-spacing: 2px;
    margin-bottom: 2rem;
    opacity: 0.95;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    animation: pulse 2s ease-in-out infinite;
    padding: 0 2rem;
}

@keyframes pulse {
    0%, 100% { opacity: 0.95; }
    50% { opacity: 1; }
}

/* Highlight badge styling */
.highlight-text {
    margin-top: 2rem;
}

.highlight-badge {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 1.2rem 2.5rem;
    border-radius: var(--radius-full);
    font-family: var(--font-family-primary);
    font-size: 1.4rem;
    font-weight: var(--font-weight-semibold);
    color: white;
    text-transform: uppercase;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.highlight-badge:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.highlight-badge i {
    font-size: 1.2rem;
    color: var(--primary-orange);
    animation: sparkle 2s ease-in-out infinite;
}

@keyframes sparkle {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(5deg); }
}

/* Registration Table Section */
.registration-table-section {
    padding: 3rem 0;
    background: transparent;
    position: relative;
    /* Performance Optimization */
    contain: layout style;
    will-change: transform;
    transform: translateZ(0);
}

.registration-table-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        linear-gradient(45deg, transparent 0%, rgba(46, 89, 144, 0.02) 25%, transparent 50%),
        linear-gradient(-45deg, transparent 0%, rgba(255, 140, 66, 0.015) 25%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

.modern-pricing-section {
    background: white;
    border-radius: 2rem;
    padding: 4rem;
    box-shadow: 
        0 20px 40px rgba(46, 89, 144, 0.1),
        0 0 0 1px rgba(46, 89, 144, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid #e2e8f0;
    position: relative;
    overflow: hidden;
    /* Performance Optimization */
    will-change: transform;
    transform: translateZ(0);
}

.modern-pricing-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: 
        radial-gradient(circle at center, rgba(46, 89, 144, 0.02) 0%, transparent 70%);
    /* Slower, less intensive animation for better performance */
    animation: rotate 60s linear infinite;
    pointer-events: none;
    z-index: 0;
    /* Performance Optimization */
    will-change: transform;
    transform: translateZ(0);
}

.modern-pricing-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 150px;
    height: 150px;
    background: 
        radial-gradient(circle, rgba(255, 140, 66, 0.08) 0%, transparent 60%);
    border-radius: 50%;
    z-index: 0;
    /* Performance Optimization */
    transform: translateZ(0);
}

@keyframes rotate {
    0% {
        transform: rotate3d(0, 0, 1, 0deg);
    }
    100% {
        transform: rotate3d(0, 0, 1, 360deg);
    }
}

/* Ensure content stays above background effects */
.modern-pricing-section > * {
    position: relative;
    z-index: 2;
}

.modern-pricing-container {
    text-align: center;
    margin-bottom: 4rem;
}

.pricing-icon {
    width: 8rem;
    height: 8rem;
    background: linear-gradient(135deg, #2E5990 0%, #4A7BC8 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    box-shadow: 0 10px 30px rgba(46, 89, 144, 0.3);
}

.pricing-icon i {
    font-size: 3rem;
    color: white;
}

.modern-pricing-title {
    font-size: 3rem;
    font-weight: 700;
    color: #1A3A5C;
    margin-bottom: 1rem;
}

.modern-pricing-subtitle {
    font-size: 1.6rem;
    color: #718096;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* Pricing Notes */
.pricing-notes {
    margin-top: 2rem;
    margin-bottom: 2rem;
    padding: 2rem;
    background: #f7fafc;
    border-radius: 1rem;
    border-left: 4px solid #FF8C42;
}

.note-item {
    margin-bottom: 1rem;
    font-size: 1.3rem;
    color: #4a5568;
    line-height: 1.6;
}

.note-item:last-child {
    margin-bottom: 0;
}

.note-item span {
    display: block;
}

/* Professional Registration Table */
.modern-pricing-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin: 2rem 0;
    font-family: 'Poppins', sans-serif;
}

.modern-pricing-table table {
    width: 100%;
    border-collapse: collapse;
}

/* Table Structure */
.pricing-table-header {
    background: linear-gradient(135deg, #2E5990 0%, #4A7BC8 100%);
    color: white;
}

.pricing-table-header th {
    padding: 15px 12px;
    text-align: center;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.pricing-table-header th:first-child {
    text-align: left;
    padding-left: 20px;
}

.pricing-table-header th:last-child {
    border-right: none;
}

.pricing-table-row {
    border-bottom: 1px solid #e2e8f0;
    transition: background-color 0.2s ease;
}

.pricing-table-row:hover {
    background-color: #f8fafc;
}

.pricing-table-row:last-child {
    border-bottom: none;
}

.pricing-table-row td {
    padding: 15px 12px;
    text-align: center;
    font-size: 14px;
    color: #2d3748;
    border-right: 1px solid #e2e8f0;
    vertical-align: middle;
}

.pricing-table-row td:first-child {
    text-align: left;
    padding-left: 20px;
    font-weight: 600;
    color: #2E5990;
}

.pricing-table-row td:last-child {
    border-right: none;
}

/* Price styling */
.price-cell {
    font-weight: 600;
    color: #2E5990;
    font-size: 15px;
}

.foreign-price {
    color: #FF8C42;
    font-weight: 600;
}

/* Register button */
.register-btn {
    background: linear-gradient(135deg, #2E5990 0%, #4A7BC8 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    white-space: nowrap;
    border: none;
    cursor: pointer;
}

.register-btn:hover {
    background: linear-gradient(135deg, #1A3A5C 0%, #2E5990 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(46, 89, 144, 0.3);
    color: white;
    text-decoration: none;
}

.register-btn i {
    font-size: 10px;
}

.contact-required {
    background: linear-gradient(135deg, #FF8C42 0%, #E6652A 100%);
}

.contact-required:hover {
    background: linear-gradient(135deg, #E6652A 0%, #d4541f 100%);
}

/* Mobile Cards Layout */
.mobile-pricing-cards {
    display: none;
}

/* Back to Home Section */
.back-to-home {
    padding: 4rem 0;
    background: white;
    /* Performance Optimization */
    contain: layout style;
    will-change: transform;
    transform: translateZ(0);
}

.back-home-content {
    text-align: center;
}

.back-home-text {
    font-size: 1.6rem;
    color: #718096;
    margin-bottom: 2rem;
}

.back-home-btn {
    display: inline-flex;
    align-items: center;
    gap: 1rem;
    background: linear-gradient(135deg, #2E5990 0%, #4A7BC8 100%);
    color: white;
    text-decoration: none;
    padding: 1.5rem 3rem;
    border-radius: 1rem;
    font-size: 1.6rem;
    font-weight: 600;
    transition: all 0.2s ease; /* Reduced transition time for better performance */
    /* Performance Optimization */
    will-change: transform;
    transform: translateZ(0);
}

.back-home-btn:hover {
    background: linear-gradient(135deg, #1A3A5C 0%, #2E5990 100%);
    transform: translateY(-1px) translateZ(0); /* Reduced transform for better performance */
    box-shadow: 0 8px 25px rgba(46, 89, 144, 0.25); /* Reduced shadow intensity */
    text-decoration: none;
    color: white;
}

/* Responsive Design */
@media (max-width: 1200px) {
    /* Registration-specific responsive styles */
    .registration-title-container {
        max-width: 750px;
        padding: 0 2rem;
    }
    
    .registration-early-bird {
        font-size: 1.5rem;
        letter-spacing: 1.5px;
        margin-bottom: 1.8rem;
        padding: 0 1rem;
    }
    
    .highlight-badge {
        font-size: 1.4rem;
        padding: 1rem 2rem;
        gap: 0.8rem;
    }
    
    /* Back to Home Button Responsive */
    .back-to-home {
        padding: 3.5rem 0;
    }
    
    .back-home-text {
        font-size: 1.5rem;
        margin-bottom: 1.8rem;
    }
    
    .back-home-btn {
        padding: 1rem 2.5rem;
        font-size: var(--font-size-base);
    }
}

@media (max-width: 1024px) {
    .registration-page {
        margin-top: 0rem;
        padding: 0rem 0;
    }
    
    .registration-title-container {
        max-width: 700px;
        padding: 0 2rem;
    }
    
    .registration-early-bird {
        font-size: 1.4rem;
        letter-spacing: 1.2px;
        margin-bottom: 1.5rem;
        padding: 0 1rem;
    }
    
    .highlight-badge {
        font-size: 1.3rem;
        padding: 0.9rem 1.8rem;
        gap: 0.7rem;
    }
    
    .highlight-badge i {
        font-size: 1rem;
    }
    
    .modern-pricing-section {
        padding: 4rem 0;
        margin-top: 2rem;
    }
    
    .modern-pricing-table {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
        border-radius: var(--radius-lg);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    }
    
    .modern-pricing-table table {
        min-width: 70rem;
        font-size: var(--font-size-sm);
    }
    
    .pricing-table-header th {
        padding: 1.5rem 1rem;
        font-size: var(--font-size-base);
    }
    
    .pricing-table-header th:first-child {
        min-width: 20rem;
        max-width: 25rem;
        position: sticky;
        left: 0;
        z-index: 2;
    }
    
    .pricing-table-row td {
        padding: 1.2rem 1rem;
        font-size: var(--font-size-sm);
    }
    
    .pricing-table-row td:first-child {
        min-width: 20rem;
        max-width: 25rem;
        position: sticky;
        left: 0;
        z-index: 1;
        background: var(--white);
        border-right: 2px solid rgba(46, 89, 144, 0.1);
    }
    
    .register-btn {
        padding: 0.8rem 1.5rem;
        font-size: var(--font-size-xs);
        min-width: 10rem;
    }
    
    .back-to-home {
        padding: 3rem 0;
    }
    
    .back-home-text {
        font-size: 1.4rem;
        margin-bottom: 1.5rem;
    }
    
    .back-home-btn {
        padding: 1rem 2rem;
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 768px) {
    .registration-page {
        margin-top: 0rem;
        padding: 1.5rem 0;
    }
    
    .registration-title-container {
        max-width: 100%;
        padding: 0 1.5rem;
    }
    
    .registration-early-bird {
        font-size: 1.2rem;
        letter-spacing: 1px;
        margin-bottom: 1.2rem;
        padding: 0 0.5rem;
        line-height: 1.4;
    }
    
    .highlight-text {
        margin-top: 1.5rem;
    }
    
    .highlight-badge {
        font-size: 1.1rem;
        padding: 0.8rem 1.5rem;
        gap: 0.6rem;
        flex-direction: column;
        text-align: center;
        line-height: 1.3;
    }
    
    .highlight-badge i {
        font-size: 0.9rem;
    }
    
    .modern-pricing-section {
        padding: 3rem 0;
    }
    
    .modern-pricing-title {
        font-size: var(--font-size-2xl);
    }
    
    .pricing-icon {
        width: 6rem;
        height: 6rem;
        margin-bottom: 1.5rem;
    }
    
    .pricing-icon i {
        font-size: var(--font-size-2xl);
    }
    
    .modern-pricing-table {
        display: none;
    }
    
    .mobile-pricing-cards {
        display: block;
    }
    
    .mobile-card {
        background: var(--white);
        border-radius: var(--radius-xl);
        margin-bottom: 2rem;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .mobile-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    }
    
    .mobile-card-header {
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
        color: white;
        padding: 2rem;
        text-align: center;
    }
    
    .mobile-card-title {
        font-size: var(--font-size-xl);
        font-weight: var(--font-weight-bold);
        margin-bottom: 0.5rem;
    }
    
    .mobile-card-subtitle {
        font-size: var(--font-size-sm);
        opacity: 0.9;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .mobile-card-body {
        padding: 2rem;
    }
    
    .mobile-price-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid var(--gray-200);
    }
    
    .mobile-price-row:last-child {
        border-bottom: none;
        margin-top: 1rem;
        padding-top: 1.5rem;
    }
    
    .mobile-price-label {
        font-weight: var(--font-weight-medium);
        color: var(--gray-700);
        font-size: var(--font-size-sm);
    }
    
    .mobile-price-value {
        font-weight: var(--font-weight-bold);
        color: var(--primary-blue-dark);
        font-size: var(--font-size-base);
    }
    
    .mobile-price-value.foreign {
        color: var(--primary-green);
    }
    
    .mobile-card-action {
        text-align: center;
        margin-top: 1.5rem;
    }
    
    .mobile-card .register-btn {
        display: inline-flex;
        align-items: center;
        gap: 0.8rem;
        padding: 1rem 2rem;
        background: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-dark));
        color: white;
        text-decoration: none;
        border-radius: var(--radius-lg);
        font-weight: var(--font-weight-semibold);
        transition: all 0.3s ease;
        font-size: var(--font-size-sm);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .back-to-home {
        padding: 2.5rem 0;
    }
    
    .back-home-text {
        font-size: 1.3rem;
        margin-bottom: 1.2rem;
        line-height: 1.4;
    }
    
    .back-home-btn {
        padding: 1rem 1.8rem;
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 480px) {
    .registration-page {
        padding-top: 0rem;
    }
    
    /* Registration-specific responsive styles */
    .registration-title-container {
        max-width: 100%;
        padding: 0 1rem;
    }
    
    .multi-color-title {
        font-size: 2.2rem;
        margin-bottom: 1.2rem;
        line-height: 1.2;
    }
    
    .title-word {
        margin: 0 0.2rem;
        font-size: 2rem;
        display: inline-block;
    }
    
    .registration-early-bird {
        font-size: 1.1rem;
        letter-spacing: 0.8px;
        margin-bottom: 1.2rem;
        padding: 0 0.5rem;
        line-height: 1.3;
    }
    
    .highlight-text {
        margin-top: 1.2rem;
        padding: 0 0.5rem;
    }
    
    .highlight-badge {
        font-size: 1rem;
        padding: 0.7rem 1.2rem;
        gap: 0.5rem;
        border-radius: 1.2rem;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
    }
    
    .highlight-badge i {
        font-size: 0.9rem;
    }
    
    .modern-pricing-section {
        padding: 1.5rem 1rem;
    }
    
    .modern-pricing-title {
        font-size: 2rem;
    }
    
    .modern-pricing-subtitle {
        font-size: 1rem;
        padding: 0 0.5rem;
    }
    
    .mobile-card {
        margin: 0 0.5rem;
        margin-bottom: 2rem;
    }
    
    .mobile-card-header {
        padding: 1.2rem;
    }
    
    .mobile-card-title {
        font-size: 1.2rem;
    }
    
    .mobile-card-subtitle {
        font-size: 0.9rem;
    }
    
    .mobile-card-body {
        padding: 1.2rem;
    }
    
    .mobile-price-row {
        padding: 0.8rem 0;
    }
    
    .mobile-price-label {
        font-size: 0.85rem;
    }
    
    .mobile-price-value {
        font-size: 1rem;
    }
    
    .mobile-card .register-btn {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
        gap: 0.4rem;
    }
    
    /* Back to Home Button Responsive */
    .back-to-home {
        padding: 2rem 0;
    }
    
    .back-home-text {
        font-size: 1.2rem;
        margin-bottom: 1.2rem;
        padding: 0 1rem;
        line-height: 1.4;
    }
    
    .back-home-btn {
        padding: 1.1rem 2rem;
        font-size: 1.2rem;
        gap: 0.6rem;
    }
}

@media (max-width: 360px) {
    /* Registration-specific responsive styles */
    .registration-title-container {
        padding: 0 0.5rem;
    }
    
    .multi-color-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }
    
    .title-word {
        margin: 0 0.1rem;
        font-size: 1.6rem;
        display: block;
    }
    
    .registration-early-bird {
        font-size: 1rem;
        letter-spacing: 0.5px;
        margin-bottom: 1rem;
        padding: 0 0.3rem;
    }
    
    .highlight-badge {
        font-size: 0.9rem;
        padding: 0.6rem 1rem;
        gap: 0.4rem;
        border-radius: 1rem;
    }
    
    .highlight-badge i {
        font-size: 0.8rem;
    }
    
    /* Back to Home Button Responsive */
    .back-to-home {
        padding: 1.5rem 0;
    }
    
    .back-home-text {
        font-size: 1.1rem;
        margin-bottom: 1rem;
        padding: 0 0.8rem;
        line-height: 1.3;
    }
    
    .back-home-btn {
        padding: 1rem 1.8rem;
        font-size: 1.1rem;
        gap: 0.5rem;
    }
}

/* Print Styles */
@media print {
    .registration-page {
        padding-top: 0;
    }
    
    .header {
        display: none;
    }
    
    .registration-header {
        background: none;
        color: black;
    }
    
    .multi-color-title {
        color: black !important;
    }
} 
.registration-early-bird{
    color: #fdfdfd;
    margin-top: 1.2rem;
}

/* Landscape Mobile and Small Tablets */
@media (max-width: 640px) {
    .registration-header {
        padding: 4.5rem 0;
        margin-top: 9rem;
    }
    
    .registration-title-container {
        max-width: 100%;
        padding: 0 1.2rem;
    }
    
    .multi-color-title {
        font-size: 2.5rem;
        margin-bottom: 1.3rem;
        line-height: 1.25;
    }
    
    .title-word {
        margin: 0 0.25rem;
        display: inline-block;
        word-break: break-word;
    }
    
    .registration-early-bird {
        font-size: 1.2rem;
        letter-spacing: 0.9px;
        margin-bottom: 1.3rem;
        padding: 0 0.5rem;
        line-height: 1.45;
    }
    
    .highlight-text {
        margin-top: 1.3rem;
        padding: 0 0.8rem;
    }
    
    .highlight-badge {
        font-size: 1.15rem;
        padding: 0.75rem 1.3rem;
        gap: 0.55rem;
        border-radius: 1.3rem;
        flex-wrap: wrap;
        justify-content: center;
        text-align: center;
    }
    
    .highlight-badge i {
        font-size: 0.95rem;
    }
    
    /* Back to Home Button Responsive */
    .back-to-home {
        padding: 2.2rem 0;
    }
    
    .back-home-text {
        font-size: 1.2rem;
        margin-bottom: 1.3rem;
        padding: 0 1rem;
    }
    
    .back-home-btn {
        padding: 1.1rem 1.8rem;
        font-size: 1.2rem;
        gap: 0.6rem;
        border-radius: 0.7rem;
    }
}

/* ========================================
   Flash Message Section Styles
   ======================================== */

.flash-message-section {
    padding: 2rem 0;
    background: linear-gradient(135deg, #fff5f5 0%, #ffffff 50%, #f0f9ff 100%);
    position: relative;
    z-index: 2;
    margin-bottom: 2rem;
}

.flash-message-card {
    background: linear-gradient(135deg, #ffffff 0%, #fefefe 100%);
    border-radius: 20px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.06),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    position: relative;
    transition: all 0.3s ease;
}

.flash-message-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #2e5990, #ff8c42, #4caf50);
    z-index: 1;
}

.flash-message-card:hover {
    transform: translateY(-2px);
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 10px 20px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.flash-message-content {
    padding: 2.5rem;
    display: flex;
    gap: 2rem;
    align-items: flex-start;
}

.flash-message-icon {
    flex-shrink: 0;
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2e5990, #3d6ba3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: 0 8px 20px rgba(46, 89, 144, 0.3);
    position: relative;
}

.flash-message-icon::after {
    content: '';
    position: absolute;
    inset: -2px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2e5990, #ff8c42);
    z-index: -1;
    opacity: 0.3;
}

.flash-message-text {
    flex: 1;
}

.flash-message-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1a365d;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.highlight-number {
    color: #ff8c42;
    font-size: 2.2rem;
    font-weight: 900;
    text-shadow: 2px 2px 4px rgba(255, 140, 66, 0.3);
}

.flash-message-subtitle {
    font-size: 1.1rem;
    color: #2e5990;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.privileges-list {
    list-style: none;
    padding: 0;
    margin: 0 0 2rem 0;
    display: grid;
    gap: 0.8rem;
}

.privileges-list li {
    display: flex;
    align-items: flex-start;
    gap: 0.8rem;
    font-size: 0.95rem;
    line-height: 1.5;
    color: #4a5568;
}

.privileges-list li i {
    color: #4caf50;
    font-size: 1rem;
    margin-top: 0.1rem;
    flex-shrink: 0;
}

.flash-message-footer {
    display: flex;
    gap: 2rem;
    align-items: flex-start;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.qr-code-section {
    text-align: center;
    flex-shrink: 0;
}

.qr-code-placeholder {
    width: 100px;
    height: 100px;
    border-radius: 8px;
    border: 2px solid #e2e8f0;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    color: #718096;
    font-size: 0.75rem;
    font-weight: 500;
}

.qr-code-placeholder i {
    font-size: 2rem;
    color: #2e5990;
}

.qr-text {
    font-size: 0.85rem;
    color: #718096;
    font-weight: 500;
}

.venue-info {
    flex: 1;
}

.venue-details h4 {
    color: #2e5990;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.venue-details h4 i {
    color: #ff8c42;
}

.venue-details p {
    margin: 0.3rem 0;
    font-size: 0.9rem;
    color: #4a5568;
    line-height: 1.4;
}

.venue-address {
    font-weight: 600;
    color: #2d3748 !important;
}

.venue-contact a {
    color: #2e5990;
    text-decoration: none;
    font-weight: 500;
}

.venue-contact a:hover {
    color: #ff8c42;
    text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 768px) {
    .flash-message-section {
        padding: 1.5rem 0;
        margin-bottom: 1.5rem;
    }

    .flash-message-content {
        flex-direction: column;
        padding: 2rem 1.5rem;
        gap: 1.5rem;
        text-align: center;
    }

    .flash-message-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        align-self: center;
    }

    .flash-message-title {
        font-size: 1.5rem;
    }

    .highlight-number {
        font-size: 1.8rem;
    }

    .privileges-list {
        text-align: left;
    }

    .flash-message-footer {
        flex-direction: column;
        gap: 1.5rem;
        text-align: center;
    }

    .venue-details {
        text-align: left;
    }
}

@media (max-width: 480px) {
    .flash-message-content {
        padding: 1.5rem 1rem;
    }

    .flash-message-title {
        font-size: 1.3rem;
    }

    .highlight-number {
        font-size: 1.6rem;
    }

    .privileges-list li {
        font-size: 0.9rem;
    }
}