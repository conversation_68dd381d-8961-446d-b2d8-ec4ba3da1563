/* About Conference Page Specific Styles */
.about-conference-section {
    padding: 30px 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    position: relative;
    overflow: hidden;
}

.about-conference-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, rgba(11, 76, 122, 0.02) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

.about-content {
    position: relative;
    z-index: 2;
}

.about-intro {
    text-align: center;
    margin-bottom: 60px;
}

.about-intro h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0B4C7A;
    margin-bottom: 20px;
    background: linear-gradient(45deg, #0B4C7A, #1E88E5);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.about-description {
    background: white;
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 50px;
    border: 1px solid #e2e8f0;
}

.about-description p {
    font-size: var(--font-size-base, 1.6rem);
    line-height: 1.8;
    color: var(--text-primary, #1A202C);
    margin-bottom: 20px;
    text-align: justify;
    font-weight: var(--font-weight-normal, 400);
}

.theme-highlight {
    background: linear-gradient(135deg, #0B4C7A 0%, #1E88E5 100%);
    color: white;
    padding: 30px;
    border-radius: 15px;
    margin: 30px 0;
    text-align: center;
}

.theme-highlight h3 {
    font-size: var(--font-size-xl, 2.4rem);
    font-weight: var(--font-weight-semibold, 600);
    margin-bottom: 15px;
    color: white;
}

.theme-highlight p {
    font-size: var(--font-size-base, 1.6rem);
    opacity: 0.95;
    margin: 0;
    color: white;
    line-height: 1.6;
}

.conference-highlights {
    padding: 30px 0;
    background: white;
}

.highlights-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.highlight-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 2px solid #f1f5f9;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    min-height: 350px;
    display: flex;
    flex-direction: column;
}

.highlight-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #0B4C7A, #1E88E5, #43A047, #FF9800);
}

.highlight-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.highlight-card h3 {
    font-size: var(--font-size-md, 1.8rem);
    font-weight: var(--font-weight-semibold, 600);
    color: #0B4C7A;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 12px;
    line-height: 1.4;
}

.highlight-card h3 i {
    font-size: var(--font-size-base, 1.6rem);
    color: #1E88E5;
}

.highlight-card p {
    color: var(--text-primary, #1A202C);
    line-height: 1.6;
    font-size: var(--font-size-base, 1.6rem);
    margin-bottom: 15px;
    flex-grow: 1;
    font-weight: var(--font-weight-normal, 400);
}

.highlight-card ul {
    margin: 15px 0;
    padding-left: 20px;
    flex-grow: 1;
}

.highlight-card li {
    color: var(--text-primary, #1A202C);
    line-height: 1.6;
    margin-bottom: 10px;
    font-size: var(--font-size-base, 1.6rem);
    font-weight: var(--font-weight-normal, 400);
}

.centennial-badge {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #1a202c;
    padding: 15px 25px;
    border-radius: 50px;
    display: inline-block;
    font-weight: 600;
    margin: 20px 0;
    box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
}

.join-cta {
    background: linear-gradient(135deg, #0B4C7A 0%, #1E88E5 100%);
    color: white;
    padding: 50px 40px;
    border-radius: 20px;
    text-align: center;
    margin-top: 50px;
}

.join-cta h3 {
    font-size: var(--font-size-2xl, 3rem);
    font-weight: var(--font-weight-semibold, 600);
    margin-bottom: 15px;
    color: white;
}

.join-cta p {
    font-size: var(--font-size-md, 1.8rem);
    margin-bottom: 30px;
    opacity: 0.95;
    color: white;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn-cta {
    display: inline-block;
    padding: 15px 30px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: var(--font-weight-semibold, 600);
    font-size: var(--font-size-sm, 1.4rem);
    transition: all 0.3s ease;
    border: 2px solid white;
}

.btn-cta.primary {
    background: white;
    color: #0B4C7A;
}

.btn-cta.primary:hover {
    background: transparent;
    color: white;
}

.btn-cta.secondary {
    background: transparent;
    color: white;
}

.btn-cta.secondary:hover {
    background: white;
    color: #0B4C7A;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@media (max-width: 768px) {
    .about-intro h2 {
        font-size: 2rem;
    }

    .about-description {
        padding: 25px;
    }

    .about-description p {
        font-size: var(--font-size-sm, 1.4rem);
    }

    .theme-highlight {
        padding: 25px;
    }

    .theme-highlight h3 {
        font-size: var(--font-size-lg, 2rem);
    }

    .theme-highlight p {
        font-size: var(--font-size-sm, 1.4rem);
    }

    .highlights-grid {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .highlight-card {
        min-height: auto;
        padding: 25px;
    }

    .highlight-card h3 {
        font-size: var(--font-size-base, 1.6rem);
    }

    .highlight-card h3 i {
        font-size: var(--font-size-sm, 1.4rem);
    }

    .highlight-card p,
    .highlight-card li {
        font-size: var(--font-size-sm, 1.4rem);
    }

    .join-cta {
        padding: 40px 25px;
    }

    .join-cta h3 {
        font-size: var(--font-size-xl, 2.4rem);
    }

    .join-cta p {
        font-size: var(--font-size-base, 1.6rem);
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn-cta {
        width: 100%;
        max-width: 250px;
        font-size: var(--font-size-sm, 1.4rem);
    }
} 