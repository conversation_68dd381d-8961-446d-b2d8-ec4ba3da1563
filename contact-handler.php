<?php
// ====================================================
// CONTACT FORM HANDLER FOR IMA NATCON 2025
// ====================================================
// Simple PHP script for Hostinger Shared Hosting

// ========== CONFIGURATION (CHANGE ONLY THIS) ==========
$your_email = "<EMAIL>"; 
$website_name = "IMA NATCON 2025";
$enable_logging = true; // Set to false to disable logging
$log_file = "contact_logs.txt"; // Log file name
// ========== END OF CONFIGURATION ==========


// Logging function
function writeLog($message) {
    global $enable_logging, $log_file;
    if ($enable_logging) {
        $timestamp = date('Y-m-d H:i:s');
        $log_entry = "[$timestamp] $message" . PHP_EOL;
        file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    }
}

// Basic security check
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    writeLog("SECURITY: Direct access attempt from IP: " . $_SERVER['REMOTE_ADDR']);
    die('Direct access not allowed');
}

// Get form data safely
$name = isset($_POST['name']) ? trim($_POST['name']) : '';
$email = isset($_POST['email']) ? trim($_POST['email']) : '';
$phone = isset($_POST['phone']) ? trim($_POST['phone']) : '';
$subject = isset($_POST['subject']) ? trim($_POST['subject']) : '';
$message = isset($_POST['message']) ? trim($_POST['message']) : '';

// Simple validation
$errors = array();

if (empty($name)) {
    $errors[] = "Name is required";
}

if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
    $errors[] = "Valid email is required";
}

if (empty($phone)) {
    $errors[] = "Phone number is required";
}

if (empty($subject)) {
    $errors[] = "Subject is required";
}

if (empty($message)) {
    $errors[] = "Message is required";
}

// If there are errors, redirect back with error
if (!empty($errors)) {
    $error_message = implode(", ", $errors);
    writeLog("VALIDATION ERROR: " . $error_message . " | From: $email | IP: " . $_SERVER['REMOTE_ADDR']);
    header("Location: contact-us.html?error=1&msg=" . urlencode($error_message));
    exit;
}

// Prepare email content
$email_subject = "New Contact Form: " . $subject;

$email_body = "
NEW MESSAGE FROM WEBSITE
========================

Name: $name
Email: $email
Phone: $phone
Subject: $subject

Message:
--------
$message

========================
Sent from: $website_name
Date: " . date('Y-m-d H:i:s') . "
";

// Email headers (important for proper delivery)
$headers = "From: $website_name <$your_email>\r\n";
$headers .= "Reply-To: $email\r\n";
$headers .= "Content-Type: text/plain; charset=UTF-8\r\n";

// Send email using PHP mail() function
$mail_sent = mail($your_email, $email_subject, $email_body, $headers);

if ($mail_sent) {
    // Success - redirect back with success message
    header("Location: contact-us.html?success=1&msg=" . urlencode("Thank you! Your message has been sent successfully."));
} else {
    // Failed - redirect back with error
    header("Location: contact-us.html?error=1&msg=" . urlencode("Sorry, there was an error sending your message. Please try again."));
}

exit;
?> 